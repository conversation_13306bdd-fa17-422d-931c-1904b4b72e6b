package com.bxm.customer.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.customer.domain.ValueAddedEmployee;
import com.bxm.customer.domain.dto.valueAdded.PersonalTaxDetailExportDTO;
import com.bxm.customer.domain.dto.valueAdded.SocialInsuranceDTO;
import com.bxm.customer.domain.query.valueAdded.EmployeeQuery;
import com.bxm.customer.domain.vo.valueAdded.ValueAddedEmployeeVO;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 增值员工信息Service接口
 *
 *
 * 支持三种业务类型的员工信息管理：
 * 1. 社医保（bizType=1）：支持提醒、更正、减员操作
 * 2. 个税明细（bizType=2）：支持提醒、更正、减员操作
 * 3. 国税账号（bizType=3）：支持会计实名、异地实名操作
 *
 * 验证层次：
 * - Service层：核心业务逻辑验证
 * - 策略层：业务特定规则验证
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
public interface IValueAddedEmployeeService extends IService<ValueAddedEmployee> {

    /**
     * 单条upsert增值员工信息
     *
     * 支持的业务类型：
     * 1. 社医保（bizType=1）：验证社保套餐信息、应发工资等
     * 2. 个税明细（bizType=2）：验证身份证号格式、应发工资等
     * 3. 国税账号（bizType=3）：验证税号格式、查询密码等
     *
     * @param employeeVO 增值员工信息VO对象，包含完整的验证注解和业务字段
     * @return 员工ID，新增或更新成功后返回员工的主键ID
     * @throws IllegalArgumentException 当参数验证失败时抛出（如必填字段为空、格式不正确等）
     * @throws RuntimeException 当业务处理失败时抛出（如数据库操作失败等）
     */
    Long upsert(@Valid @NotNull ValueAddedEmployeeVO employeeVO);

    /**
     * 根据交付单编号和身份证号查询员工信息
     *
     * @param deliveryOrderNo 交付单编号
     * @param idNumber 身份证号
     * @param bizType 业务类型
     * @return 员工信息，如果不存在则返回null
     */
    ValueAddedEmployee getByDeliveryOrderAndIdNumber(String deliveryOrderNo, String idNumber, Integer bizType);

    /**
     * 根据交付单编号和手机号查询员工信息
     *
     * @param deliveryOrderNo 交付单编号
     * @param mobile 手机号
     * @param bizType 业务类型
     * @return 员工信息，如果不存在则返回null
     */
    ValueAddedEmployee getByDeliveryOrderAndMobile(String deliveryOrderNo, String mobile, Integer bizType);

    /**
     * 验证员工信息的核心业务逻辑
     *
     * 注意：基础字段验证（如非空、格式等）已通过@Valid注解在VO类上处理，
     * 此方法只验证核心业务逻辑，如业务类型有效性等。
     *
     * @param employeeVO 员工信息VO
     * @throws IllegalArgumentException 当核心业务验证失败时抛出
     */
    void validateEmployee(ValueAddedEmployeeVO employeeVO);

    /**
     * 获取社保明细数据用于Excel导出
     *
     * @param deliveryOrderNo 交付单编号
     * @return 社保明细导出数据列表
     */
    List<SocialInsuranceDTO> getSocialInsuranceDetailForExport(String deliveryOrderNo);

    /**
     * 获取个税明细数据用于Excel导出
     *
     * @param deliveryOrderNo 交付单编号
     * @return 个税明细导出数据列表
     */
    List<PersonalTaxDetailExportDTO> getPersonalTaxDetailForExport(String deliveryOrderNo);

    /**
     * 批量新增社保员工信息（重新设计）
     * deliveryOrderNo将从文件记录中获取，不再作为参数传入
     *
     * @param fileId 文件ID
     * @param bizType 业务类型
     * @param overrideExisting 是否覆盖现有数据
     */
    void processBatchEmployeeData(Long fileId, Integer bizType, Boolean overrideExisting);

    /**
     * 根据文件ID获取处理状态
     *
     * @param fileId 文件ID
     * @return 处理状态信息
     */
    String getProcessStatusByFileId(Long fileId);

    /**
     * 增值员工信息条件查询
     *
     * 支持按业务类型进行分页查询，所有条件在 Service 中动态拼接
     * 根据不同的bizType返回相应转换的EmployeeVO对象
     *
     * @param query 查询参数，包含bizType等条件
     * @return VO结果列表，根据bizType进行适当转换
     */
    List<ValueAddedEmployeeVO> query(EmployeeQuery query);

    /**
     * 批量删除增值员工信息
     *
     * @param ids 员工ID列表
     * @return 删除是否成功
     * @throws IllegalArgumentException 当参数为空或无效时抛出
     * @throws RuntimeException 当删除操作失败时抛出
     */
    boolean batchDelete(List<Long> ids);
}
