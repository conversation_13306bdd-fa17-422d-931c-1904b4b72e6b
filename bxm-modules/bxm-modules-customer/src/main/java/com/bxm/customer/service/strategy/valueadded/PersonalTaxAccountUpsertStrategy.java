package com.bxm.customer.service.strategy.valueadded;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.customer.domain.ValueAddedEmployee;
import com.bxm.customer.domain.enums.ValueAddedBizType;
import com.bxm.customer.domain.enums.ValueAddedOperationType;
import com.bxm.customer.mapper.ValueAddedEmployeeMapper;
import com.bxm.customer.service.strategy.AbstractEmployeeUpsertStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 个税账号业务类型的upsert策略实现
 *
 * <AUTHOR>
 * @date 2025-08-02
 */
@Slf4j
@Component
public class PersonalTaxAccountUpsertStrategy extends AbstractEmployeeUpsertStrategy {

    @Autowired
    private ValueAddedEmployeeMapper valueAddedEmployeeMapper;

    @Override
    public Integer getSupportedBizType() {
        return ValueAddedBizType.PERSONAL_TAX_ACCOUNT.getCode();
    }

    @Override
    public void validateBusinessFields(ValueAddedEmployee employee) {
        // 验证操作类型是否适用于个税账号业务
        if (!ValueAddedOperationType.isValidForBizType(ValueAddedBizType.PERSONAL_TAX_ACCOUNT, employee.getOperationType())) {
            throw new IllegalArgumentException("个税账号业务不支持的操作类型: " + employee.getOperationType());
        }

        // 验证个税账号业务必填字段：账号、登录密码
        if (StringUtils.isEmpty(employee.getTaxNumber())) {
            throw new IllegalArgumentException("个税账号的账号不能为空");
        }

        if (StringUtils.isEmpty(employee.getQueryPassword())) {
            throw new IllegalArgumentException("个税账号的登录密码不能为空");
        }
    }

    @Override
    protected void doPreprocess(ValueAddedEmployee employee) {
        // 标准化税号（去除空格，转大写）
        if (StringUtils.isNotEmpty(employee.getTaxNumber())) {
            employee.setTaxNumber(employee.getTaxNumber().trim().toUpperCase());
        }

        // 标准化身份证号（去除空格，转大写）
        if (StringUtils.isNotEmpty(employee.getIdNumber())) {
            employee.setIdNumber(employee.getIdNumber().trim().toUpperCase());
        }

        // 标准化手机号（去除空格）
        if (StringUtils.isNotEmpty(employee.getMobile())) {
            employee.setMobile(employee.getMobile().trim());
        }

        // 清理其他业务类型专用字段
        employee.setSocialInsurance(null);
        employee.setGrossSalary(null);
        employee.setProvidentFundPersonal(null);

        // 处理扩展信息：构建个税账号业务的扩展信息
        Map<String, Object> bizTypeExtendInfo = buildBizTypeExtendInfo(
                ValueAddedBizType.PERSONAL_TAX_ACCOUNT,
                ValueAddedOperationType.PERSONAL_TAX_ACCOUNT_ADD
        );

        // 合并现有扩展信息和业务类型扩展信息
        employee.setExtendInfo(mergeExtendInfo(employee.getExtendInfo(), bizTypeExtendInfo));
    }

    @Override
    protected void doPostprocess(ValueAddedEmployee employee, boolean isUpdate) {

    }

    @Override
    public ValueAddedEmployee findExistingEmployee(ValueAddedEmployee employee) {
        // 个税账号业务的唯一性判断：交付单编号 + 身份证号 + 业务类型
        // 也可以考虑税号的唯一性
        LambdaQueryWrapper<ValueAddedEmployee> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ValueAddedEmployee::getDeliveryOrderNo, employee.getDeliveryOrderNo())
                .eq(ValueAddedEmployee::getIdNumber, employee.getIdNumber())
                .eq(ValueAddedEmployee::getBizType, ValueAddedBizType.PERSONAL_TAX_ACCOUNT.getCode());

        return valueAddedEmployeeMapper.selectOne(queryWrapper);
    }

    @Override
    protected void doMerge(ValueAddedEmployee existing, ValueAddedEmployee newEmployee) {
        // 更新个税账号特定字段
        if (StringUtils.isNotEmpty(newEmployee.getTaxNumber())) {
            existing.setTaxNumber(newEmployee.getTaxNumber());
        }
        if (StringUtils.isNotEmpty(newEmployee.getQueryPassword())) {
            existing.setQueryPassword(newEmployee.getQueryPassword());
        }
        if (StringUtils.isNotEmpty(newEmployee.getLoginMethod())) {
            existing.setLoginMethod(newEmployee.getLoginMethod());
        }
        if (StringUtils.isNotEmpty(newEmployee.getRealNameAgent())) {
            existing.setRealNameAgent(newEmployee.getRealNameAgent());
        }

        // 更新基础信息
        if (StringUtils.isNotEmpty(newEmployee.getEmployeeName())) {
            existing.setEmployeeName(newEmployee.getEmployeeName());
        }
        if (StringUtils.isNotEmpty(newEmployee.getMobile())) {
            existing.setMobile(newEmployee.getMobile());
        }
        if (StringUtils.isNotEmpty(newEmployee.getRemark())) {
            existing.setRemark(newEmployee.getRemark());
        }

        // 合并扩展信息
        Map<String, Object> existingExtendInfo = parseExtendInfo(existing.getExtendInfo());
        Map<String, Object> newExtendInfo = parseExtendInfo(newEmployee.getExtendInfo());
        existingExtendInfo.putAll(newExtendInfo);
        existing.setExtendInfo(JSON.toJSONString(existingExtendInfo));

        // 更新操作类型和状态
        existing.setOperationType(newEmployee.getOperationType());
    }

    /**
     * 获取操作类型名称
     */
    private String getOperationTypeName(Integer operationType) {
        if (operationType == null) {
            return "未知操作";
        }

        // 使用枚举获取操作类型名称
        if (operationType == 1) {
            return ValueAddedOperationType.PERSONAL_TAX_ACCOUNT_ADD.getName();
        }

        return "未知操作(" + operationType + ")";
    }



    /**
     * 脱敏敏感信息
     */
    private String maskSensitiveInfo(String info) {
        if (StringUtils.isEmpty(info) || info.length() <= 4) {
            return info;
        }
        return info.substring(0, 2) + "****" + info.substring(info.length() - 2);
    }
}
